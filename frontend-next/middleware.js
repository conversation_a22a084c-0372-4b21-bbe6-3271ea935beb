import { NextResponse } from 'next/server';

export function middleware(request) {
  const response = NextResponse.next();

  // Handle CORS for development
  if (process.env.NODE_ENV === 'development') {
    const origin = request.headers.get('origin');
    const referer = request.headers.get('referer');

    // Check if request is from allowed origins
    if (origin === 'https://aiworkspace.01sworld.top' ||
        (referer && referer.includes('aiworkspace.01sworld.top'))) {
      response.headers.set('Access-Control-Allow-Origin', 'https://aiworkspace.01sworld.top');
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      response.headers.set('Access-Control-Allow-Credentials', 'true');
    }
  }

  return response;
}

export const config = {
  matcher: [
    '/_next/:path*',
    '/api/:path*',
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
